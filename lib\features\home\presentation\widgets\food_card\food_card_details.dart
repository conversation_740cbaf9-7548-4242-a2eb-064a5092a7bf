import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/common/widgets/app_image.dart';
import 'package:cal/generated/assets.dart';
import 'package:cal/generated/locale_keys.g.dart';
import 'package:cal/core/local_models/food_model/food_model.dart';
import 'package:cal/common/extentions/colors_extension.dart';
import 'loading_text_animation.dart';
import 'shimmer_line.dart';

class FoodCardDetails extends StatelessWidget {
  const FoodCardDetails({
    super.key,
    required this.foodModel,
    required this.isLoading,
  });

  final FoodModel foodModel;
  final bool isLoading;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Padding(
        padding: const EdgeInsetsDirectional.symmetric(horizontal: 10),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeaderRow(context),
            const SizedBox(height: 3),
            _buildCaloriesRow(context),
            const SizedBox(height: 4),
            if (!isLoading) _buildMacrosRow(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderRow(BuildContext context) {
    return isLoading
        ? const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [LoadingTextAnimation(), SizedBox(height: 12), ShimmerLine()],
          )
        : Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AppText.labelLarge(
                (context.locale.languageCode == 'en' ? foodModel.englishName : foodModel.arabicName) ?? 'Unknown',
                color: context.onSecondary,
                fontWeight: FontWeight.w500,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
              const Spacer(),
              _buildTimeChip(context),
            ],
          );
  }

  Widget _buildTimeChip(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color: context.onPrimaryColor,
      ),
      padding: const EdgeInsets.all(5),
      child: isLoading
          ? const ShimmerLine(width: 50)
          : AppText.labelMedium(
              DateFormat('h:mm a').format(foodModel.date!),
              color: context.onSecondary,
              fontWeight: FontWeight.w500,
            ),
    );
  }

  Widget _buildCaloriesRow(BuildContext context) {
    return Row(
      children: [
        if (!isLoading) ...[
          const AppImage.asset(Assets.imagesCals),
          const SizedBox(width: 5),
          AppText.bodyMedium(
            foodModel.calories?.toString() ?? 'Unknown',
            color: context.onSecondary,
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(width: 5),
          AppText.bodyMedium(
            LocaleKeys.home_calorie.tr(),
            color: context.onSecondary,
            fontWeight: FontWeight.bold,
          ),
          const Spacer(),
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: foodModel.isHalal != false ? const Color(0xff27AE60) : Colors.red,
            ),
            margin: const EdgeInsetsDirectional.symmetric(horizontal: 0, vertical: 4),
            padding: const EdgeInsetsDirectional.symmetric(horizontal: 5, vertical: 2),
            child: Text(
              foodModel.isHalal != false ? LocaleKeys.common_halal.tr() : LocaleKeys.common_haram.tr(),
              style: context.textTheme.labelMedium!.copyWith(color: context.onPrimaryColor, fontWeight: FontWeight.bold),
            ),
          ),
        ] else
          const ShimmerLine(width: 50),
      ],
    );
  }

  Widget _buildMacrosRow(BuildContext context) {
    return Row(
      children: [
        if (foodModel.protein != null) ...[
          Expanded(
            child: _buildMacroItem(
              context,
              Assets.imagesProtien,
              '${foodModel.protein} ${LocaleKeys.home_gram.tr()}',
            ),
          ),
          const SizedBox(width: 2),
        ],
        if (foodModel.carbs != null) ...[
          Expanded(
            child: _buildMacroItem(
              context,
              Assets.imagesCarbs,
              '${foodModel.carbs} ${LocaleKeys.home_gram.tr()}',
            ),
          ),
          const SizedBox(width: 2),
        ],
        if (foodModel.fat != null) ...[
          Expanded(
            child: _buildMacroItem(
              context,
              Assets.imagesFats,
              '${foodModel.fat} ${LocaleKeys.home_gram.tr()}',
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildMacroItem(BuildContext context, String assetPath, String text) {
    return Row(
      children: [
        AppImage.asset(assetPath),
        const SizedBox(width: 2.5),
        AppText.labelMedium(text, color: context.onSecondary, fontWeight: FontWeight.bold),
      ],
    );
  }
}
